{"name": "LiverAppV3", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@clerk/clerk-expo": "^2.14.2", "@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.1.1", "@react-navigation/native": "^7.1.6", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/checkbox": "^1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "^1.2.0", "@rn-primitives/radio-group": "^1.2.0", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/switch": "^1.2.0", "@rn-primitives/toggle": "^1.2.0", "@rn-primitives/tooltip": "~1.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "convex": "^1.25.2", "expo": "^53.0.17", "expo-linking": "~7.1.7", "expo-navigation-bar": "~4.2.7", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.60.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.28.0", "@biomejs/biome": "2.0.6", "@types/react": "~19.0.10", "typescript": "^5.8.3"}, "private": true}