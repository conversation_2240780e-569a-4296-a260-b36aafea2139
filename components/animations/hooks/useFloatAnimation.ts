import { useCallback, useEffect, useRef } from "react";
import { Easing, type EasingFunction, type ViewStyle } from "react-native";
import {
  type SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withSpring,
} from "react-native-reanimated";
import { ENTRANCE_ANIMATIONS } from "../constants/animationConfig";

export interface FloatAnimationConfig {
  /** The amount of vertical movement in pixels (default: 10) */
  verticalOffset?: number;
  /** The amount of horizontal movement in pixels (default: 0) */
  horizontalOffset?: number;
  /** The duration of the animation in milliseconds (default: 2000) */
  duration?: number;
  /** The easing function to use (default: Easing.inOut(Easing.ease)) */
  easing?: EasingFunction;
}

export interface FloatAnimationReturn {
  animatedStyle: ViewStyle;
  translateY: SharedValue<number>;
  translateX: SharedValue<number>;
}

export function useFloatAnimation(
  config: FloatAnimationConfig = {}
): FloatAnimationReturn {
  const {
    verticalOffset = 10,
    horizontalOffset = 0,
    duration = 2000,
    easing = Easing.inOut(Easing.ease),
  } = config;

  const translateY = useSharedValue(0);
  const translateX = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: translateY.value },
      { translateX: translateX.value },
    ],
  }));

  return { animatedStyle, translateY, translateX };
}
