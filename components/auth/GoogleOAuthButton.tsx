import { Pressable, View } from "react-native";
import { Text } from "~/components/ui/text";
import { cn } from "~/lib/utils";
import { LoadingSpinner } from "./LoadingSpinner";

interface GoogleOAuthButtonProps {
	onPress: () => void;
	isLoading?: boolean;
	disabled?: boolean;
	className?: string;
	iconOnly?: boolean;
}

export function GoogleOAuthButton({
	onPress,
	isLoading = false,
	disabled = false,
	className,
	iconOnly = false,
}: GoogleOAuthButtonProps) {
	return (
		<Pressable
			onPress={onPress}
			disabled={disabled || isLoading}
			className={cn(
				"flex-row items-center justify-center bg-white border border-gray-300 rounded-xl",
				"active:bg-gray-50 active:scale-[0.98] shadow-lg shadow-gray-200/50",
				"web:transition-all web:duration-150",
				(disabled || isLoading) && "opacity-50",
				iconOnly ? "p-4" : "px-6 py-4",
				className,
			)}
			accessibilityRole="button"
			accessibilityLabel="Continue with Google"
			accessibilityState={{ disabled: disabled || isLoading }}
		>
			{isLoading ? (
				<LoadingSpinner size="sm" />
			) : (
				<View
					className={
						iconOnly ? "items-center justify-center" : "flex-row items-center"
					}
				>
					{/* Enhanced Google Logo */}
					<View
						className={cn(
							"w-6 h-6 items-center justify-center",
							!iconOnly && "mr-3",
						)}
					>
						<View className="relative w-5 h-5">
							{/* Google G logo with proper colors */}
							<View className="w-5 h-5 rounded-sm overflow-hidden bg-white border border-gray-200">
								{/* Blue section */}
								<View className="absolute top-0 left-0 w-full h-2.5 bg-blue-500" />
								{/* Red section */}
								<View className="absolute top-0 right-0 w-2.5 h-full bg-red-500" />
								{/* Yellow section */}
								<View className="absolute bottom-0 left-0 w-2.5 h-2.5 bg-yellow-500" />
								{/* Green section */}
								<View className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500" />
								{/* White G cutout */}
								<View className="absolute top-1 left-1 w-3 h-3 bg-white rounded-sm">
									<Text className="text-xs font-bold text-blue-600 text-center leading-3">
										G
									</Text>
								</View>
							</View>
						</View>
					</View>
					{!iconOnly && (
						<Text className="text-gray-700 font-semibold text-base">
							Continue with Google
						</Text>
					)}
				</View>
			)}
		</Pressable>
	);
}
