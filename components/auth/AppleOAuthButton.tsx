import { Pressable, View } from "react-native";
import * as Svg from "react-native-svg";
import { Text } from "~/components/ui/text";
import { cn } from "~/lib/utils";
import { Button } from "../ui/button";
import { LoadingSpinner } from "./LoadingSpinner";

interface AppleOAuthButtonProps {
	onPress: () => void;
	isLoading?: boolean;
	disabled?: boolean;
	className?: string;
	iconOnly?: boolean;
}

export function AppleOAuthButton({
	onPress,
	isLoading = false,
	disabled = false,
	className,
	iconOnly = false,
}: AppleOAuthButtonProps) {
	return (
		<Button
			size="icon"
			onPress={onPress}
			disabled={disabled || isLoading}
			className={cn(
				"flex-row items-center justify-center bg-black rounded-xl border border-gray-800",
				"active:bg-gray-900 active:scale-[0.98] shadow-lg shadow-black/20",
				"web:transition-all web:duration-150",
				(disabled || isLoading) && "opacity-50",
				iconOnly ? "p-4" : "px-6 py-4",
				className,
			)}
			accessibilityRole="button"
			accessibilityLabel="Continue with Apple"
			accessibilityState={{ disabled: disabled || isLoading }}
		>
			{isLoading ? (
				<LoadingSpinner size="sm" />
			) : (
				<View
					className={
						iconOnly ? "items-center justify-center" : "flex-row items-center"
					}
				>
					{/* Enhanced Apple Logo */}
					<View
						className={cn(
							"w-6 h-6 items-center justify-center",
							!iconOnly && "mr-3",
						)}
					>
						<View className="relative">
							{/* Apple logo body */}
							<View
								className="w-5 h-6 bg-white rounded-full"
								style={{
									borderTopLeftRadius: 12,
									borderTopRightRadius: 12,
									borderBottomLeftRadius: 8,
									borderBottomRightRadius: 8,
								}}
							/>
							{/* Apple logo leaf */}
							<View className="absolute -top-0.5 right-1 w-1.5 h-2 bg-white rounded-full rotate-45" />
							{/* Apple logo bite */}
							<View className="absolute top-1 right-0 w-2 h-2 bg-black rounded-full" />
						</View>
					</View>
					{!iconOnly && (
						<Text className="text-white font-semibold text-base">
							Continue with Apple
						</Text>
					)}
				</View>
			)}
		</Button>
	);
}
