import { use<PERSON><PERSON> } from "@clerk/clerk-expo";
import * as AuthSession from "expo-auth-session";
import * as <PERSON><PERSON>rowser from "expo-web-browser";
import { useCallback } from "react";
import { View } from "react-native";
import { AppleOAuthButton } from "./AppleOAuthButton";
import { GoogleOAuthButton } from "./GoogleOAuthButton";

// Handle any pending authentication sessions
WebBrowser.maybeCompleteAuthSession();

interface OAuthSectionProps {
	onSuccess?: () => void;
	onError?: (error: unknown) => void;
	isLoading?: boolean;
	disabled?: boolean;
}

export function OAuthSection({
	onSuccess,
	onError,
	isLoading = false,
	disabled = false,
}: OAuthSectionProps) {
	// Use the useSSO hook to access the startSSOFlow method
	const { startSSOFlow } = useSSO();

	const handleOAuthPress = useCallback(
		async (strategy: "oauth_google" | "oauth_apple") => {
			try {
				// Start the authentication process by calling startSSOFlow
				const { createdSessionId, setActive, signIn, signUp } =
					await startSSOFlow({
						strategy,
						// For native, you must pass a scheme using AuthSession.makeRedirectUri
						redirectUrl: AuthSession.makeRedirectUri(),
					});

				// If sign in was successful, set the active session
				if (createdSessionId) {
					setActive?.({ session: createdSessionId });
					onSuccess?.();
				} else {
					// If there is no createdSessionId,
					// there are missing requirements, such as MFA
					// Use the signIn or signUp returned from startSSOFlow
					// to handle next steps
					console.log("Additional steps required:", { signIn, signUp });

					// Handle the case where additional verification is needed
					if (
						signIn?.status === "needs_identifier" ||
						signUp?.status === "missing_requirements"
					) {
						// Handle missing requirements - this could be MFA, email verification, etc.
						// You might want to navigate to a different screen or show additional UI
						onError?.(new Error("Additional verification required"));
					}
				}
			} catch (err) {
				// Handle OAuth errors according to Clerk's error handling patterns
				console.error("OAuth Error:", JSON.stringify(err, null, 2));

				onError?.(err);
			}
		},
		[startSSOFlow, onSuccess, onError],
	);

	const handleGooglePress = useCallback(() => {
		handleOAuthPress("oauth_google");
	}, [handleOAuthPress]);

	const handleApplePress = useCallback(() => {
		handleOAuthPress("oauth_apple");
	}, [handleOAuthPress]);

	return (
		<View className="flex-row gap-x-4 justify-center">
			<AppleOAuthButton
				onPress={handleApplePress}
				isLoading={isLoading}
				disabled={disabled}
				className="h-14 w-14 rounded-xl"
				iconOnly={true}
			/>

			<GoogleOAuthButton
				onPress={handleGooglePress}
				isLoading={isLoading}
				disabled={disabled}
				className="h-14 w-14 rounded-xl"
				iconOnly={true}
			/>
		</View>
	);
}
