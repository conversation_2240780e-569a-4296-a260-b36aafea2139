import { useAuth, useSignIn, useSignUp } from "@clerk/clerk-expo";
import { router } from "expo-router";
import { useEffect, useRef, useState } from "react";

export interface AuthError {
	message: string;
	code?: string;
}

export interface SignInData {
	email: string;
	password: string;
}

export interface SignUpData {
	email: string;
	password: string;
	firstName?: string;
	lastName?: string;
}

export interface ForgotPasswordData {
	email: string;
}

export interface VerifyEmailData {
	code: string;
}

export function useClerkAuth() {
	// Track if this is the first mount to control animations
	const isFirstMount = useRef(true);

	const { signOut } = useAuth();
	const { signIn, setActive } = useSignIn();
	const { signUp, setActive: setActiveSignUp } = useSignUp();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<AuthError | null>(null);

	const clearError = () => setError(null);

	useEffect(() => {
		isFirstMount.current = false;
	}, []);

	const handleSignIn = async (data: SignInData) => {
		if (!signIn) return;

		setIsLoading(true);
		setError(null);

		try {
			const result = await signIn.create({
				identifier: data.email,
				password: data.password,
			});

			console.log(result);

			if (result.status === "complete") {
				await setActive({ session: result.createdSessionId });
				router.replace("/(tabs)");
			} else {
				setError({
					message: "Sign in incomplete. Please try again.",
					code: "INCOMPLETE_SIGNIN",
				});
			}
		} catch (err: any) {
			setError({
				message:
					err.errors?.[0]?.message || "Failed to sign in. Please try again.",
				code: err.errors?.[0]?.code || "SIGNIN_ERROR",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleSignUp = async (data: SignUpData) => {
		if (!signUp) return;

		setIsLoading(true);
		setError(null);

		try {
			const result = await signUp.create({
				emailAddress: data.email,
				password: data.password,
				firstName: data.firstName,
				lastName: data.lastName,
			});

			if (result.status === "missing_requirements") {
				// Email verification required
				await signUp.prepareEmailAddressVerification({
					strategy: "email_code",
				});
				router.push("/(auth)/verify-email");
			} else if (result.status === "complete") {
				await setActiveSignUp({ session: result.createdSessionId });
				router.replace("/(tabs)");
			}
		} catch (err: any) {
			setError({
				message:
					err.errors?.[0]?.message ||
					"Failed to create account. Please try again.",
				code: err.errors?.[0]?.code || "SIGNUP_ERROR",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleForgotPassword = async (data: ForgotPasswordData) => {
		if (!signIn) return;

		setIsLoading(true);
		setError(null);

		try {
			await signIn
				.create({
					strategy: "reset_password_email_code",
					identifier: data.email,
				})
				.then((_) => {
					router.push("/(auth)/verify-email");
					setError(null);
				})
				.catch((err) => {
					console.error("error", err.errors[0].longMessage);
					setError(err.errors[0].longMessage);
				});
		} catch (err: any) {
			console.log(err);
			setError({
				message:
					err.errors?.[0]?.message ||
					"Failed to send reset email. Please try again.",
				code: err.errors?.[0]?.code || "RESET_ERROR",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleVerifyEmail = async (data: VerifyEmailData) => {
		if (!signUp) return;

		setIsLoading(true);
		setError(null);

		try {
			const result = await signUp.attemptEmailAddressVerification({
				code: data.code,
			});

			if (result.status === "complete") {
				await setActiveSignUp({ session: result.createdSessionId });
				router.replace("/(tabs)");
			}
		} catch (err: any) {
			setError({
				message:
					err.errors?.[0]?.message ||
					"Invalid verification code. Please try again.",
				code: err.errors?.[0]?.code || "VERIFICATION_ERROR",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleSignOut = async () => {
		setIsLoading(true);
		setError(null);

		try {
			await signOut();
			router.replace("/(auth)");
		} catch (err: any) {
			setError({
				message: "Failed to sign out. Please try again.",
				code: "SIGNOUT_ERROR",
			});
		} finally {
			setIsLoading(false);
		}
	};

	return {
		error,
		isFirstMount,
		isLoading,
		clearError,
		signIn: handleSignIn,
		signUp: handleSignUp,
		resetPassword: handleForgotPassword,
		verifyEmail: handleVerifyEmail,
		signOut: handleSignOut,
	} as const;
}
