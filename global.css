@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 217 32% 96%;
    --secondary-foreground: 217 91% 60%;
    --muted: 220 14% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 220 14% 96%;
    --accent-foreground: 217 91% 60%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;
    --radius: 0.5rem;
    --chart-1: 217 91% 60%;
    --chart-2: 217 32% 96%;
    --chart-3: 220 14% 96%;
    --chart-4: 215 16% 47%;
    --chart-5: 0 84% 60%;
  }

  .dark:root {
    --background: 240 5% 6%;
    --foreground: 220 14% 96%;
    --card: 240 4% 9%;
    --card-foreground: 220 14% 96%;
    --popover: 240 4% 9%;
    --popover-foreground: 220 14% 96%;
    --primary: 217 91% 60%;
    --primary-foreground: 240 5% 6%;
    --secondary: 240 4% 16%;
    --secondary-foreground: 220 14% 96%;
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;
    --accent: 240 4% 16%;
    --accent-foreground: 220 14% 96%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 220 14% 96%;
    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 217 91% 60%;
    --chart-1: 217 91% 60%;
    --chart-2: 240 4% 16%;
    --chart-3: 240 5% 65%;
    --chart-4: 220 14% 96%;
    --chart-5: 0 63% 31%;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
