import { Link, useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { View } from "react-native";
import Animated, {
	FadeInDown,
	FadeInUp,
	useAnimatedStyle,
	useSharedValue,
	withDelay,
	withRepeat,
	withSequence,
	withSpring,
} from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";
import { AuthCard } from "~/components/auth/AuthCard";
import FormField from "~/components/form/FormField";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useAuthForm } from "~/hooks/auth/useAuthForm";
import { useClerkAuth } from "~/hooks/auth/useClerkAuth";
import { AlertCircle, Heart } from "~/lib/icons";
import {
	type EmailVerificationFormData,
	emailVerificationSchema,
} from "~/types/auth";

// Create animated components
const AnimatedButton = Animated.createAnimatedComponent(Button);

export default function VerifyEmailScreen() {
	const router = useRouter();
	const [resendCooldown, setResendCooldown] = useState(0);

	const { verifyEmail, isLoading, error, clearError } = useClerkAuth();

	// Animation values
	const buttonScale = useSharedValue(1);
	const heartScale = useSharedValue(1);
	const heartIconScale = useSharedValue(1);
	const codeInputScale = useSharedValue(1);

	const {
		control,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
	} = useAuthForm<EmailVerificationFormData>({
		schema: emailVerificationSchema,
		onSubmit: async (data) => {
			try {
				clearError();
				await verifyEmail({ code: data.code });
				router.replace("/(tabs)");
			} catch (err) {
				console.log(err);
				// Error is handled by useClerkAuth hook
				reset(); // Clear the form on error
				// Shake animation on error
				codeInputScale.value = withSequence(
					withSpring(1.05, { damping: 8 }),
					withSpring(0.95, { damping: 8 }),
					withSpring(1, { damping: 8 }),
				);
			}
		},
	});

	// Heart pulse animation
	useEffect(() => {
		const pulse = () => {
			heartScale.value = withSpring(1.1, { damping: 2 }, () => {
				heartScale.value = withSpring(1, { damping: 2 });
			});
		};

		const interval = setInterval(pulse, 2000);
		return () => clearInterval(interval);
	}, [heartScale]);

	// Heart icon continuous pulse
	useEffect(() => {
		heartIconScale.value = withRepeat(
			withSequence(
				withSpring(1.2, { damping: 4 }),
				withSpring(1, { damping: 4 }),
			),
			-1,
			false,
		);
	}, [heartIconScale]);

	// Animation styles
	const buttonAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: buttonScale.value }],
	}));

	const heartAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: heartScale.value }],
	}));

	const heartIconAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: heartIconScale.value }],
	}));

	const codeInputAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: codeInputScale.value }],
	}));

	const handleButtonPressIn = () => {
		buttonScale.value = withSpring(0.95);
	};

	const handleButtonPressOut = () => {
		buttonScale.value = withSpring(1);
	};

	const handleResendCode = async () => {
		if (resendCooldown > 0) return;

		try {
			clearError();
			// In a real implementation, you would call a resend verification email function
			// For now, we'll just show a success message and start cooldown
			setResendCooldown(60);

			const interval = setInterval(() => {
				setResendCooldown((prev) => {
					if (prev <= 1) {
						clearInterval(interval);
						return 0;
					}
					return prev - 1;
				});
			}, 1000);
		} catch (err) {
			console.log(err);
			// Error handling
		}
	};

	return (
		<SafeAreaView className="flex-1 bg-gray-50 dark:bg-background">
			<View className="flex-1 justify-center px-6 py-8">
				<Animated.View entering={FadeInUp.delay(200).duration(800)}>
					<AuthCard
						title="Verify Your Email"
						subtitle="Enter the 6-digit code sent to your email"
						logoSize="md"
						contentClassName="gap-y-4"
					>
						{/* Error Display */}
						{(errors.root?.message || error) && (
							<Animated.View
								entering={FadeInDown.duration(400)}
								className="flex-row items-start gap-x-3 p-4 bg-red-50 dark:bg-red-500/10 border border-red-200 dark:border-red-500/30 rounded-xl"
							>
								<AlertCircle size={20} className="text-red-500 mt-0.5" />
								<Text className="text-red-500 text-sm flex-1 font-medium">
									{errors.root?.message || error?.message}
								</Text>
							</Animated.View>
						)}

						{/* Verification Form */}
						<Animated.View
							entering={FadeInDown.delay(300).duration(600)}
							className="gap-y-4"
						>
							<View className="items-center gap-y-3">
								<Animated.View
									style={heartAnimatedStyle}
									className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full items-center justify-center"
								>
									<Animated.View style={heartIconAnimatedStyle}>
										<Heart
											size={24}
											className="text-blue-600 dark:text-blue-400"
										/>
									</Animated.View>
								</Animated.View>
								<Text className="text-gray-600 dark:text-gray-400 text-center leading-6">
									We've sent a verification code to your email address. Please
									check your inbox and enter the code below.
								</Text>
							</View>

							<Animated.View
								entering={FadeInDown.delay(400).duration(600)}
								style={codeInputAnimatedStyle}
							>
								<FormField
									control={control}
									name="code"
									label="Verification Code"
									placeholder="Enter 6-digit code"
									keyboardType="number-pad"
									autoCapitalize="none"
									autoComplete="one-time-code"
									maxLength={6}
									error={errors.code}
									className="text-center text-2xl tracking-widest font-mono"
								/>
							</Animated.View>

							<AnimatedButton
								entering={FadeInDown.delay(500).duration(600)}
								style={buttonAnimatedStyle}
								onPress={handleSubmit}
								onPressIn={handleButtonPressIn}
								onPressOut={handleButtonPressOut}
								disabled={isLoading || isSubmitting}
								className="h-14 bg-blue-500 hover:bg-blue-600 active:bg-blue-600 rounded-xl shadow-lg shadow-blue-500/25"
							>
								<Text className="text-lg font-semibold text-white">
									{isLoading || isSubmitting ? "Verifying..." : "Verify Email"}
								</Text>
							</AnimatedButton>
						</Animated.View>

						{/* Resend Section */}
						<Animated.View
							entering={FadeInDown.delay(600).duration(600)}
							className="gap-y-3"
						>
							<View className="items-center gap-y-2">
								<Text className="text-gray-600 dark:text-muted-foreground text-center">
									Didn't receive the code?
								</Text>

								<Button
									onPress={handleResendCode}
									disabled={resendCooldown > 0}
									variant="outline"
									className="h-12 border-gray-200 dark:border-border disabled:opacity-50"
								>
									<Text className="text-blue-600 dark:text-blue-400 font-medium">
										{resendCooldown > 0
											? `Resend in ${resendCooldown}s`
											: "Resend Code"}
									</Text>
								</Button>
							</View>

							{/* Footer Links */}
							<Animated.View
								entering={FadeInDown.delay(700).duration(600)}
								className="flex-row items-center justify-center"
							>
								<Text className="text-gray-600 dark:text-muted-foreground">
									Wrong email?{" "}
								</Text>
								<Link href="/(auth)/sign-up" asChild>
									<Button variant="ghost" className="p-0 h-auto">
										<Text className="text-blue-600 dark:text-blue-400 font-semibold">
											Go back
										</Text>
									</Button>
								</Link>
							</Animated.View>
						</Animated.View>
					</AuthCard>
				</Animated.View>
			</View>
		</SafeAreaView>
	);
}
