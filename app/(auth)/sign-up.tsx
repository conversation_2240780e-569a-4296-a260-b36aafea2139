import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { ScrollView, View } from "react-native";
import Animated, {
	FadeInDown,
	FadeInUp,
	useAnimatedStyle,
	useSharedValue,
	withSpring,
} from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";
import Form<PERSON>ield from "~/components/form/FormField";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useAuthForm } from "~/hooks/auth/useAuthForm";
import { useClerkAuth } from "~/hooks/auth/useClerkAuth";
import { AlertCircle, CircleUserRound, Heart, Shield } from "~/lib/icons";
import { type SignupFormData, signupSchema } from "~/types/auth";

// Create animated components
const AnimatedButton = Animated.createAnimatedComponent(Button);

export default function SignUpScreen() {
	const router = useRouter();
	const [acceptedTerms, setAcceptedTerms] = useState(false);

	const { signUp, isLoading, error } = useClerkAuth();

	// Animation values
	const buttonScale = useSharedValue(1);
	const checkboxScale = useSharedValue(1);
	const userIconScale = useSharedValue(1);

	const {
		control,
		handleSubmit,
		formState: { errors, isSubmitting },
	} = useAuthForm<SignupFormData>({
		schema: signupSchema,
		defaultValues: {
			name: "",
			email: "",
			password: "",
			confirmPassword: "",
		},
		onSubmit: async (data) => {
			if (!acceptedTerms) {
				// Handle terms acceptance error
				return;
			}

			try {
				// Extract name parts for Clerk
				const nameParts = data.name.trim().split(" ");
				const firstName = nameParts[0] || "";
				const lastName = nameParts.slice(1).join(" ") || "";

				await signUp({
					firstName,
					lastName,
					email: data.email,
					password: data.password,
				});
				router.push("/(auth)/verify-email");
			} catch (err) {
				console.log(err);
				// Error is handled by useClerkAuth hook
			}
		},
	});

	// User icon pulse animation
	useEffect(() => {
		const pulse = () => {
			userIconScale.value = withSpring(1.1, { damping: 2 }, () => {
				userIconScale.value = withSpring(1, { damping: 2 });
			});
		};

		const interval = setInterval(pulse, 4000);
		return () => clearInterval(interval);
	}, [userIconScale]);

	// Animation styles
	const buttonAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: buttonScale.value }],
	}));

	const checkboxAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: checkboxScale.value }],
	}));

	const userIconAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: userIconScale.value }],
	}));

	const handleButtonPressIn = () => {
		buttonScale.value = withSpring(0.95);
	};

	const handleButtonPressOut = () => {
		buttonScale.value = withSpring(1);
	};

	const handleCheckboxPress = () => {
		checkboxScale.value = withSpring(0.9, {}, () => {
			checkboxScale.value = withSpring(1);
		});
		setAcceptedTerms(!acceptedTerms);
	};

	const handleLogin = () => {
		router.push("/(auth)/sign-in");
	};

	return (
		<SafeAreaView className="flex-1 bg-gray-50">
			<ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
				<View className="flex-1 px-6 justify-center py-8">
					{/* Header Illustration */}
					<Animated.View
						entering={FadeInUp.delay(200).duration(800)}
						className="items-center mb-8"
					>
						<View className="w-50 h-32 justify-center items-center relative">
							{/* Health Illustration */}
							<Animated.View
								style={userIconAnimatedStyle}
								className="bg-blue-50 rounded-full p-5 z-10"
							>
								<CircleUserRound size={50} className="text-blue-600" />
							</Animated.View>
							<View className="absolute w-full h-full">
								<Animated.View
									entering={FadeInUp.delay(400).duration(600)}
									className="absolute w-4 h-4 bg-blue-100 rounded-full top-3 left-6"
								/>
								<Animated.View
									entering={FadeInUp.delay(600).duration(600)}
									className="absolute w-3 h-3 bg-purple-100 rounded-full bottom-6 right-8"
								/>
								<Animated.View
									entering={FadeInUp.delay(500).duration(600)}
									className="absolute w-4 h-4 justify-center items-center top-8 right-4"
								>
									<Text className="text-blue-500 text-sm font-bold">+</Text>
								</Animated.View>
								<Animated.View
									entering={FadeInUp.delay(700).duration(600)}
									className="absolute w-4 h-4 justify-center items-center bottom-3 left-4"
								>
									<Text className="text-blue-500 text-sm font-bold">+</Text>
								</Animated.View>
							</View>
						</View>
					</Animated.View>

					{/* Sign Up Title */}
					<Animated.Text
						entering={FadeInDown.delay(300).duration(600)}
						className="text-3xl font-bold text-gray-800 text-center mb-8"
					>
						Create Account
					</Animated.Text>

					{/* Error Display */}
					{(errors.root?.message || error) && (
						<Animated.View
							entering={FadeInDown.duration(400)}
							className="flex-row items-start gap-x-3 p-4 bg-red-50 border border-red-200 rounded-xl mb-6"
						>
							<AlertCircle size={20} className="text-red-500 mt-0.5" />
							<Text className="text-red-500 text-sm flex-1 font-medium">
								{errors.root?.message || error?.message}
							</Text>
						</Animated.View>
					)}

					{/* Name Input */}
					<Animated.View
						entering={FadeInDown.delay(400).duration(600)}
						className="mb-4"
					>
						<FormField
							control={control}
							name="name"
							placeholder="Full Name"
							autoCapitalize="words"
							autoComplete="name"
							error={errors.name}
							icon={<CircleUserRound size={20} className="text-gray-400" />}
						/>
					</Animated.View>

					{/* Email Input */}
					<Animated.View
						entering={FadeInDown.delay(500).duration(600)}
						className="mb-4"
					>
						<FormField
							control={control}
							name="email"
							placeholder="Email"
							keyboardType="email-address"
							autoCapitalize="none"
							autoComplete="email"
							error={errors.email}
							icon={<Heart size={20} className="text-gray-400" />}
						/>
					</Animated.View>

					{/* Password Input */}
					<Animated.View
						entering={FadeInDown.delay(600).duration(600)}
						className="mb-4"
					>
						<FormField
							control={control}
							name="password"
							placeholder="Password"
							secureTextEntry={true}
							autoCapitalize="none"
							autoComplete="new-password"
							error={errors.password}
							icon={<Shield size={20} className="text-gray-400" />}
						/>
					</Animated.View>

					{/* Confirm Password Input */}
					<Animated.View
						entering={FadeInDown.delay(700).duration(600)}
						className="mb-6"
					>
						<FormField
							control={control}
							name="confirmPassword"
							placeholder="Confirm Password"
							secureTextEntry={true}
							autoCapitalize="none"
							autoComplete="new-password"
							error={errors.confirmPassword}
							icon={<Shield size={20} className="text-gray-400" />}
						/>
					</Animated.View>

					{/* Terms and Conditions */}
					<Animated.View
						entering={FadeInDown.delay(800).duration(600)}
						className="flex-row items-start gap-x-3 py-1 mb-6"
					>
						<Button
							variant="ghost"
							size="icon"
							onPress={handleCheckboxPress}
							className="w-6 h-6 p-0 mt-0.5"
						>
							<Animated.View
								style={checkboxAnimatedStyle}
								className={`w-5 h-5 border-2 rounded ${
									acceptedTerms
										? "bg-blue-500 border-blue-500"
										: "border-gray-300"
								} items-center justify-center`}
							>
								{acceptedTerms && (
									<Animated.Text
										entering={FadeInDown.duration(200)}
										className="text-white text-xs font-bold"
									>
										✓
									</Animated.Text>
								)}
							</Animated.View>
						</Button>
						<View className="flex-1">
							<Text className="text-sm text-gray-600 leading-5">
								I agree to the{" "}
								<Text className="text-blue-600 font-medium">
									Terms of Service
								</Text>{" "}
								and{" "}
								<Text className="text-blue-600 font-medium">
									Privacy Policy
								</Text>
							</Text>
						</View>
					</Animated.View>

					{/* Sign Up Button */}
					<AnimatedButton
						entering={FadeInDown.delay(900).duration(600)}
						style={buttonAnimatedStyle}
						onPress={handleSubmit}
						onPressIn={handleButtonPressIn}
						onPressOut={handleButtonPressOut}
						disabled={isLoading || isSubmitting || !acceptedTerms}
						className={`rounded-xl h-14 items-center mb-6 shadow-lg ${
							isLoading || isSubmitting || !acceptedTerms
								? "bg-gray-400"
								: "bg-blue-500 hover:bg-blue-600 active:bg-blue-600"
						}`}
					>
						<Text className="text-white text-base font-semibold">
							{isLoading || isSubmitting ? "Creating Account..." : "Sign Up"}
						</Text>
					</AnimatedButton>

					{/* Login Link */}
					<Animated.View
						entering={FadeInDown.delay(1000).duration(600)}
						className="flex-row justify-center items-center"
					>
						<Text className="text-gray-500 text-sm">
							Already have an account?{" "}
						</Text>
						<Button
							variant="ghost"
							onPress={handleLogin}
							className="p-0 h-auto"
						>
							<Text className="text-blue-500 text-sm font-semibold">Login</Text>
						</Button>
					</Animated.View>
				</View>
			</ScrollView>
		</SafeAreaView>
	);
}
