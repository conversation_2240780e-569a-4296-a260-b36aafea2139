import { Link } from "expo-router";
import { useEffect } from "react";
import { TouchableOpacity, View } from "react-native";
import Animated, {
	FadeInDown,
	FadeInUp,
	useAnimatedStyle,
	useSharedValue,
	withSpring,
} from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";
import Form<PERSON>ield from "~/components/form/FormField";
import { Text } from "~/components/ui/text";
import { useAuthForm } from "~/hooks/auth/useAuthForm";
import { useClerkAuth } from "~/hooks/auth/useClerkAuth";
import { useWarmUpBrowser } from "~/hooks/auth/useWarmUpBrowser";
import { AlertCircle, Heart, Shield } from "~/lib/icons";
import { type LoginFormData, loginSchema } from "~/types/auth";

// Create animated components
const AnimatedTouchableOpacity =
	Animated.createAnimatedComponent(TouchableOpacity);

export default function SignInScreen() {
	useWarmUpBrowser();

	const { signIn, isLoading, error } = useClerkAuth();

	// Animation values
	const buttonScale = useSharedValue(1);
	const heartScale = useSharedValue(1);

	const {
		control,
		handleSubmit,
		formState: { errors, isSubmitting },
	} = useAuthForm<LoginFormData>({
		schema: loginSchema,
		defaultValues: {
			email: "",
			password: "",
		},
		onSubmit: async (data) => {
			try {
				await signIn({ email: data.email, password: data.password });
			} catch (err: unknown) {
				console.log(err);
				// Error is handled by useClerkAuth hook
			}
		},
	});

	// Heart pulse animation
	useEffect(() => {
		const pulse = () => {
			heartScale.value = withSpring(1.1, { damping: 2 }, () => {
				heartScale.value = withSpring(1, { damping: 2 });
			});
		};

		const interval = setInterval(pulse, 3000);
		return () => clearInterval(interval);
	}, [heartScale]);

	// Button animation styles
	const buttonAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: buttonScale.value }],
	}));

	const heartAnimatedStyle = useAnimatedStyle(() => ({
		transform: [{ scale: heartScale.value }],
	}));

	const handleButtonPressIn = () => {
		buttonScale.value = withSpring(0.95);
	};

	const handleButtonPressOut = () => {
		buttonScale.value = withSpring(1);
	};

	return (
		<SafeAreaView className="flex-1 bg-gray-50">
			<View className="flex-1 px-6 justify-center">
				{/* Header Illustration */}
				<Animated.View
					entering={FadeInUp.delay(200).duration(800)}
					className="items-center mb-10"
				>
					<View className="w-50 h-38 justify-center items-center relative">
						{/* Liver Health Illustration */}
						<Animated.View
							style={heartAnimatedStyle}
							className="bg-blue-50 rounded-full p-5 z-10"
						>
							<Heart size={60} className="text-blue-500" />
						</Animated.View>
						<View className="absolute w-full h-full">
							<Animated.View
								entering={FadeInUp.delay(400).duration(600)}
								className="absolute w-5 h-5 bg-indigo-100 rounded-full top-5 left-8"
							/>
							<Animated.View
								entering={FadeInUp.delay(600).duration(600)}
								className="absolute w-4 h-4 bg-red-100 rounded-full bottom-8 right-10"
							/>
							<Animated.View
								entering={FadeInUp.delay(500).duration(600)}
								className="absolute w-4 h-4 justify-center items-center top-10 right-5"
							>
								<Text className="text-blue-500 text-sm font-bold">+</Text>
							</Animated.View>
							<Animated.View
								entering={FadeInUp.delay(700).duration(600)}
								className="absolute w-4 h-4 justify-center items-center bottom-5 left-5"
							>
								<Text className="text-blue-500 text-sm font-bold">+</Text>
							</Animated.View>
						</View>
					</View>
				</Animated.View>

				{/* Login Title */}
				<Animated.Text
					entering={FadeInDown.delay(300).duration(600)}
					className="text-3xl font-bold text-gray-800 text-center mb-10"
				>
					Login
				</Animated.Text>

				{/* Error Display */}
				{(errors.root?.message || error) && (
					<Animated.View
						entering={FadeInDown.duration(400)}
						className="flex-row items-start gap-x-3 p-4 bg-red-50 border border-red-200 rounded-xl mb-4"
					>
						<AlertCircle size={20} className="text-red-500 mt-0.5" />
						<Text className="text-red-500 text-sm flex-1 font-medium">
							{errors.root?.message || error?.message}
						</Text>
					</Animated.View>
				)}

				{/* Email Input */}
				<Animated.View entering={FadeInDown.delay(400).duration(600)}>
					<FormField
						control={control}
						name="email"
						placeholder="Email"
						keyboardType="email-address"
						autoCapitalize="none"
						autoComplete="email"
						error={errors.email}
						icon={<Heart size={20} className="text-gray-400" />}
					/>
				</Animated.View>

				{/* Password Input */}
				<Animated.View entering={FadeInDown.delay(500).duration(600)}>
					<FormField
						control={control}
						name="password"
						placeholder="Password"
						secureTextEntry={true}
						autoCapitalize="none"
						autoComplete="current-password"
						error={errors.password}
						icon={<Shield size={20} className="text-gray-400" />}
					/>
				</Animated.View>

				{/* Forgot Password */}
				<Animated.View entering={FadeInDown.delay(600).duration(600)}>
					<Link href="/(auth)/forgot-password" asChild>
						<TouchableOpacity className="items-center mt-2 mb-8">
							<Text className="text-gray-500 text-sm">Forgot Password?</Text>
						</TouchableOpacity>
					</Link>
				</Animated.View>

				{/* Login Button */}
				<AnimatedTouchableOpacity
					entering={FadeInDown.delay(700).duration(600)}
					style={buttonAnimatedStyle}
					className={`rounded-xl py-4 items-center mb-6 shadow-lg ${
						isLoading || isSubmitting ? "bg-gray-400" : "bg-blue-500"
					}`}
					onPress={handleSubmit}
					onPressIn={handleButtonPressIn}
					onPressOut={handleButtonPressOut}
					disabled={isLoading || isSubmitting}
				>
					<Text className="text-white text-base font-semibold">
						{isLoading || isSubmitting ? "Logging in..." : "Login"}
					</Text>
				</AnimatedTouchableOpacity>

				{/* Sign Up Link */}
				<Animated.View
					entering={FadeInDown.delay(800).duration(600)}
					className="flex-row justify-center items-center"
				>
					<Text className="text-gray-500 text-sm">
						{"Don't you have an account? "}
					</Text>
					<Link href="/(auth)/sign-up" asChild>
						<TouchableOpacity>
							<Text className="text-blue-500 text-sm font-semibold">
								Sign Up
							</Text>
						</TouchableOpacity>
					</Link>
				</Animated.View>
			</View>
		</SafeAreaView>
	);
}
